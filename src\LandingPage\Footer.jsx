import React from 'react';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Youtube } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-6">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold mb-4 text-secondary">Brand Hunt</h3>
            <p className="text-gray-400 mb-6">
              BRAND HUNT TECHNICAL SERVICES L.L.C delivers high-quality, reliable solutions for residential and commercial spaces across the UAE.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-secondary transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-secondary transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-secondary transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-secondary transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-secondary transition-colors">
                <Youtube size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <a href="/" className="text-gray-400 hover:text-secondary transition-colors">Home</a>
              </li>
              <li>
                <a href="/about" className="text-gray-400 hover:text-secondary transition-colors">About Us</a>
              </li>
              <li>
                <a href="/" className="text-gray-400 hover:text-secondary transition-colors">Our Services</a>
              </li>
              <li>
                <a href="/" className="text-gray-400 hover:text-secondary transition-colors">Projects</a>
              </li>
              <li>
                <a href="/contact" className="text-gray-400 hover:text-secondary transition-colors">Contact Us</a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Our Services</h4>
            <ul className="space-y-3">
              <li>
                <a href="/products/floor-wall-tiling" className="text-gray-400 hover:text-secondary transition-colors">Floor & Wall Tiling</a>
              </li>
              <li>
                <a href="/products/ac-ventilation" className="text-gray-400 hover:text-secondary transition-colors">AC & Ventilation</a>
              </li>
              <li>
                <a href="/products/carpentry-wood" className="text-gray-400 hover:text-secondary transition-colors">Carpentry Works</a>
              </li>
              <li>
                <a href="/products/electromechanical" className="text-gray-400 hover:text-secondary transition-colors">Electromechanical</a>
              </li>
              <li>
                <a href="/products/plumbing" className="text-gray-400 hover:text-secondary transition-colors">Plumbing Services</a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Contact Info</h4>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 text-secondary mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-400">V09 Russian Cluster international City, Dubai, UAE</span>
              </li>
              <li className="flex items-start">
                <Mail className="h-5 w-5 text-secondary mt-1 mr-3 flex-shrink-0" />
                <div>
                  <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-secondary transition-colors block">
                    <EMAIL> </a>
                  
                </div>
              </li>
              <li className="flex items-start">
                <Phone className="h-5 w-5 text-secondary mt-1 mr-3 flex-shrink-0" />
                <div>
                  <a href="tel:+00971508442283" className="text-gray-400 hover:text-secondary transition-colors block">
                    00971508442283
                  </a>

                </div>
              </li>
            </ul>
          </div>
        </div>


        {/* Copyright */}
        <div className="mt-12 pt-8 border-t border-gray-800 text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} BRAND HUNT TECHNICAL SERVICES L.L.C. All Rights Reserved.
          </p>
          <div className="flex justify-center space-x-6 mt-4">
            <a href="#" className="text-gray-400 hover:text-secondary transition-colors text-sm">Privacy Policy</a>
            <a href="#" className="text-gray-400 hover:text-secondary transition-colors text-sm">Terms of Service</a>
            <a href="#" className="text-gray-400 hover:text-secondary transition-colors text-sm">Sitemap</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
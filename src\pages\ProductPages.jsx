import React, { useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import productList from '../data/ProductList';
import { motion } from 'framer-motion';

const ProductPage = () => {
  const { slug } = useParams();
  const product = productList.find((item) => item.slug === slug);
  const containerRef = useRef(null);

  const imageVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.7 } },
  };

  const textVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.7 } },
  };

  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3, 
        delayChildren: 0.2, 
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  if (!product) {
    return (
      <div className="text-center py-20 text-2xl font-roboto text-gray-800">
        Product Not Found
      </div>
    );
  }

  return (
    <div className="bg-white" ref={containerRef}>
      {/* Hero Section */}
      <div className="relative w-full h-[60vh] sm:h-[70vh] overflow-hidden bg-black">
        <motion.img
          src={product.heroImage}
          alt={product.title}
          className="w-full h-full object-cover"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          whileHover={{ scale: 1.05 }}
        />
        <div className="absolute inset-0 bg-black bg-opacity-50" />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-4 z-10">
          <motion.h1
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold font-roboto text-secondary mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            {product.title}
          </motion.h1>
          {product.heroDescription && (
            <motion.p
              className="max-w-3xl text-base sm:text-lg md:text-xl text-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              {product.heroDescription}
            </motion.p>
          )}
        </div>
      </div>

      {product.relatedServices && product.relatedServices.length > 0 && (
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-16 space-y-16">
          {product.relatedServices.map((service, index) => (
            <motion.div
              key={index}
              className={`grid md:grid-cols-2 gap-8 items-center ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }} 
              variants={sectionVariants}
            >
              {/* Image */}
              <motion.div
                className={`${index % 2 === 1 ? 'md:order-2' : ''}`}
                variants={imageVariants}
              >
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-72 object-cover rounded-lg shadow-md"
                />
              </motion.div>

              {/* Text */}
              <motion.div
                className={`${index % 2 === 1 ? 'md:order-1' : ''}`}
                variants={textVariants}
              >
                <h2 className="text-2xl sm:text-3xl font-bold text-secondary mb-4">
                  {service.title}
                </h2>
                <p className="text-gray-600 text-base sm:text-lg leading-relaxed">
                  {service.description}
                </p>
              </motion.div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Sections */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 pb-12 sm:pb-20">
        {product.sections?.map((section, idx) => (
          <motion.div
            key={idx}
            className="mb-12 sm:mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }} 
            variants={sectionVariants}
          >
            <motion.h2
              className="text-xl sm:text-2xl font-bold text-center font-roboto text-gray-800 mb-6 mt-6"
              variants={itemVariants}
            >
              {section.heading}
            </motion.h2>
            <motion.p
              className="text-base sm:text-lg text-gray-800 font-roboto mx-auto mb-6"
              variants={itemVariants}
            >
              {section.sectionDescription}
            </motion.p>
            <motion.div
              className={`grid gap-4 sm:gap-6 justify-items-center ${
                section.items.length === 1
                  ? 'grid-cols-1'
                  : section.items.length === 2
                  ? 'grid-cols-1 sm:grid-cols-2'
                  : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
              }`}
              variants={sectionVariants}
            >
              {section.items.map((item, i) => (
                <motion.div
                  key={i}
                  className="flex flex-col items-center text-center"
                  variants={itemVariants}
                >
                  <img
                    src={item.image}
                    alt={item.label}
                    className="w-full max-w-[20rem] h-48 object-cover rounded-lg shadow-md"
                  />
                  <p className="mt-3 text-base sm:text-lg font-semibold font-roboto text-gray-800">
                    {item.label}
                  </p>
                  <p className="mt-1 text-sm text-gray-600 font-roboto">
                    {item.description}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default ProductPage;
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Send } from 'lucide-react';


const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    setTimeout(() => {
      console.log('Form submitted:', formData);
      setIsSubmitting(false);
      setSubmitSuccess(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
      });

      setTimeout(() => setSubmitSuccess(false), 5000);
    }, 1500);
  };

  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.2,
      },
    },
  };

  const childVariants = {
    hidden: { opacity: 0, y: isReducedMotion ? 0 : 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: isReducedMotion ? 1 : 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.7, ease: 'easeOut' },
    },
  };

  return (
    <div className="bg-gray-50">
      {/* Hero Section */}
      <motion.div
        className="relative bg-secondary/70 py-20"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={sectionVariants}
      >
        <div className="absolute inset-0 z-0"></div>
        <div className="max-w-7xl mx-auto px-6 text-center relative z-10">
          <motion.h1
            className="text-4xl font-bold tracking-tight text-white sm:text-5xl"
            variants={childVariants}
          >
            Get in Touch
          </motion.h1>
          <motion.p
            className="mt-6 text-xl text-white max-w-3xl mx-auto"
            variants={childVariants}
          >
            We're here to help with all your technical service needs
          </motion.p>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-16 sm:py-24">
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={sectionVariants}
        >
          {/* Contact Form */}
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg"
            variants={sectionVariants}
          >
            <motion.h2
              className="text-2xl font-bold text-gray-900 mb-6"
              variants={childVariants}
            >
              Send us a message
            </motion.h2>

            {submitSuccess && (
              <motion.div
                className="mb-6 p-4 bg-green-100 text-green-700 rounded-lg"
                variants={imageVariants}
                initial="hidden"
                animate="visible"
              >
                Thank you! Your message has been sent successfully. We'll contact you soon.
              </motion.div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <motion.div
                className="grid grid-cols-1 gap-6 sm:grid-cols-2"
                variants={sectionVariants}
              >
                <motion.div variants={childVariants}>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent"
                  />
                </motion.div>

                <motion.div variants={childVariants}>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent"
                  />
                </motion.div>
              </motion.div>

              <motion.div
                className="grid grid-cols-1 gap-6 sm:grid-cols-2"
                variants={sectionVariants}
              >
                <motion.div variants={childVariants}>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent"
                  />
                </motion.div>

                <motion.div variants={childVariants}>
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent"
                  >
                    <option value="">Select a subject</option>
                    <option value="General Inquiry">General Inquiry</option>
                    <option value="Service Request">Service Request</option>
                    <option value="Quote Request">Quote Request</option>
                    <option value="Complaint">Complaint</option>
                    <option value="Other">Other</option>
                  </select>
                </motion.div>
              </motion.div>

              <motion.div variants={childVariants}>
                <label
                  htmlFor="message"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  value={formData.message}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent"
                ></textarea>
              </motion.div>

              <motion.div variants={childVariants}>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center justify-center w-full bg-secondary hover:bg-secondary/90 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  {isSubmitting ? (
                    'Sending...'
                  ) : (
                    <>
                      <Send className="h-5 w-5 mr-2" />
                      Send Message
                    </>
                  )}
                </button>
              </motion.div>
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            variants={sectionVariants}
          >
            <motion.div variants={sectionVariants}>
              <motion.h2
                className="text-2xl font-bold text-gray-900 mb-6"
                variants={childVariants}
              >
                Contact Information
              </motion.h2>
              <motion.p
                className="text-gray-600 mb-6"
                variants={childVariants}
              >
                Have questions about our services or need immediate assistance? Reach out to us through any of these channels.
              </motion.p>
            </motion.div>

            <motion.div
              className="space-y-6"
              variants={sectionVariants}
            >
              {[
                {
                  title: 'Email Us',
                  icon: <Mail className="h-6 w-6" />,
                  info: '<EMAIL>',
                },
                {
                  title: 'Call Us',
                  icon: <Phone className="h-6 w-6" />,
                  info: '00971508442283',
                },
                {
                  title: 'Visit Us',
                  icon: <MapPin className="h-6 w-6" />,
                  info: 'V09 Russian Cluster, International City\nDubai, United Arab Emirates',
                },
              ].map((contact, index) => (
                <motion.div
                  key={index}
                  className="flex items-start"
                  variants={childVariants}
                >
                  <div className="flex-shrink-0 bg-secondary/10 p-3 rounded-full text-secondary">
                    {contact.icon}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{contact.title}</h3>
                    <p className="text-gray-600 whitespace-pre-line">{contact.info}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              className="pt-8 border-t border-gray-200"
              variants={sectionVariants}
            >
              <motion.h3
                className="text-lg font-medium text-gray-900 mb-4"
                variants={childVariants}
              >
                Business Hours
              </motion.h3>
              <motion.ul
                className="space-y-2 text-gray-600"
                variants={sectionVariants}
              >
                {[
                  { day: 'Monday - Friday', hours: '8:00 AM - 6:00 PM' },
                  { day: 'Saturday', hours: '9:00 AM - 4:00 PM' },
                  { day: 'Sunday', hours: 'Closed' },
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    className="flex justify-between"
                    variants={childVariants}
                  >
                    <span>{item.day}</span>
                    <span>{item.hours}</span>
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>

            <motion.div
              className="pt-8 border-t border-gray-200"
              variants={sectionVariants}
            >
              <motion.h3
                className="text-lg font-medium text-gray-900 mb-4"
                variants={childVariants}
              >
                Emergency Services
              </motion.h3>
              <motion.p
                className="text-gray-600 mb-4"
                variants={childVariants}
              >
                For urgent technical service needs outside business hours, please call our emergency hotline:
              </motion.p>
              <motion.a
                href="tel:00971508442283"
                className="text-secondary font-bold"
                variants={childVariants}
              >
                00971508442283
              </motion.a>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
      <div className="bg-gray-200 py-12">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Our Location
          </h2>
          <div className="rounded-xl overflow-hidden shadow-xl">
            <iframe
              title="Brand Hunt Location"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3610.093541857839!2d55.40091531536389!3d25.169242783888783!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f61bc5051fa1d%3A0x9e83e9b5590ecbb5!2sBrand%20Hunt%20Technical%20Services%20LLC!5e0!3m2!1sen!2sae!4v1718900000000!5m2!1sen!2sae"
              width="100%"
              height="450"
              style={{ border: 0 }}
              allowFullScreen=""
              loading="lazy"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
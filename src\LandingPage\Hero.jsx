import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom'; 
import HeroBg from '../assets/images/About.jpg';

const Hero = () => {
  const navigate = useNavigate(); 


  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.2
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const handleRequestQuote = () => {
    navigate('/contact'); 
  };

  return (
    <motion.section 
      className="text-textcolor min-h-[85vh] flex items-center relative"
      style={{
        backgroundImage: `url(${HeroBg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
      initial="hidden"
      animate="visible"
      variants={container}
    >
      <div className="absolute inset-0 bg-black/40 z-0"></div>
      
      <div className="max-w-7xl mx-auto px-6 py-12 grid grid-cols-1 md:grid-cols-2 gap-8 relative z-10">
        <motion.div 
          className="flex flex-col justify-center space-y-6"
          variants={container}
        >
          <motion.h1 
            className="text-3xl md:text-4xl font-bold leading-tight"
            variants={item}
          >
            Your Trusted Partner for<br className="hidden md:block" />
            <span className="text-secondary"> Comprehensive Technical Services</span>
          </motion.h1>
          
          <motion.p 
            className="text-lg text-white/90"
            variants={item}
          >
            From tiling to HVAC, plumbing to plastering — <strong>BRAND HUNT TECHNICAL SERVICES L.L.C</strong> delivers high-quality, reliable solutions for residential and commercial spaces across the UAE.
          </motion.p>
          
          <motion.div 
            className="flex space-x-4 pt-8"
            variants={item}
          >
            <button 
              onClick={handleRequestQuote}
              className="bg-secondary text-textcolor px-6 py-3 rounded-lg text-sm font-semibold hover:bg-secondary/80 transition-all"
            >
              Request a Quote
            </button>
            <button className="border border-white/60 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:bg-white/10 transition-all">
              Learn More
            </button>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Hero;
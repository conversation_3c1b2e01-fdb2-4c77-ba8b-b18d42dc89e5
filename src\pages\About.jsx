import React from 'react';
import { motion } from 'framer-motion';
import AboutImg from '../assets/images/hero1.jpg';
import AboutImage from '../assets/images/Hero.jpg';
import { Mail, Phone, MapPin } from 'lucide-react';


const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

const About = () => {
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.2,
      },
    },
  };


  const childVariants = {
    hidden: { opacity: 0, y: isReducedMotion ? 0 : 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  const imageVariants = {
    hidden: { opacity: 0, x: isReducedMotion ? 0 : -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.7, ease: 'easeOut' },
    },
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <motion.div
        className="relative min-h-[80vh] flex items-center"
        style={{
          backgroundImage: `url(${AboutImg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={sectionVariants}
      >
        <div className="absolute inset-0 bg-black/60 z-0"></div>
        <div className="max-w-7xl mx-auto px-6 py-24 text-center relative z-10">
          <motion.h1
            className="text-4xl font-bold tracking-tight text-white sm:text-5xl"
            variants={childVariants}
          >
            ABOUT US
          </motion.h1>
          <motion.p
            className="mt-6 text-xl text-gray-300 max-w-3xl mx-auto"
            variants={childVariants}
          >
            BRAND HUNT TECHNICAL SERVICES L.L.C is committed to delivering top-tier technical solutions with precision and reliability. 
            With expertise across multiple trades, we serve residential and commercial clients throughout the UAE.
            Our mission is to ensure quality, safety, and customer satisfaction in every project we undertake.
          </motion.p>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-16 sm:py-24">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionVariants}
        >
          <motion.div variants={imageVariants}>
            <img
              src={AboutImage}
              alt="Our team at work"
              className="rounded-lg shadow-xl w-full h-auto"
            />
          </motion.div>
          <motion.div variants={sectionVariants}>
            <motion.h2
              className="text-3xl font-bold text-gray-900 mb-6"
              variants={childVariants}
            >
              Our Story
            </motion.h2>
            <motion.p
              className="text-lg text-gray-600 mb-6"
              variants={childVariants}
            >
              BRAND HUNT TECHNICAL SERVICES L.L.C was founded with a vision to provide comprehensive, high-quality technical services to both residential and commercial clients across the United Arab Emirates. Since our establishment, we have grown to become a trusted name in the industry.
            </motion.p>
            <motion.p
              className="text-lg text-gray-600 mb-6"
              variants={childVariants}
            >
              Our journey began with a small team of dedicated professionals, and today we proudly employ over 50 skilled technicians and engineers specializing in various technical fields.
            </motion.p>
            <motion.div
              className="bg-gray-50 p-6 rounded-lg"
              variants={sectionVariants}
            >
              <motion.h3
                className="text-xl font-semibold text-gray-900 mb-4"
                variants={childVariants}
              >
                Why Choose Us?
              </motion.h3>
              <motion.ul className="space-y-3" variants={sectionVariants}>
                {[
                  '15+ years of combined industry experience',
                  'Licensed and insured professionals',
                  'Quality materials and workmanship',
                  '24/7 emergency services',
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    variants={childVariants}
                  >
                    <svg
                      className="h-6 w “… w-6 text-secondary flex-shrink-0"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span className="ml-3 text-gray-700">{item}</span>
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Our Values Section */}
        <motion.div
          className="mt-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionVariants}
        >
          <motion.h2
            className="text-3xl font-bold text-center text-gray-900 mb-12"
            variants={childVariants}
          >
            Our Core Values
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={sectionVariants}
          >
            {[
              {
                title: 'Excellence',
                icon: (
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                ),
                description:
                  'We commit to delivering superior quality in every project, no matter the size or complexity.',
              },
              {
                title: 'Integrity',
                icon: (
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                ),
                description:
                  'Honesty and transparency guide every interaction with our clients and partners.',
              },
              {
                title: 'Teamwork',
                icon: (
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                ),
                description:
                  'Collaboration and mutual respect drive our success and foster innovation.',
              },
            ].map((value, index) => (
              <motion.div
                key={index}
                className="bg-gray-50 p-6 rounded-lg text-center"
                variants={childVariants}
              >
                <div className="mx-auto h-12 w-12 rounded-full bg-secondary flex items-center justify-center text-white mb-4">
                  {value.icon}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {value.title}
                </h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Contact Info */}
        <motion.div
          className="mt-24 bg-gray-50 rounded-lg p-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionVariants}
        >
          <motion.h2
            className="text-3xl font-bold text-gray-900 mb-8 text-center"
            variants={childVariants}
          >
            Get In Touch
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={sectionVariants}
          >
            {[
              {
                title: 'Email Us',
                icon: <Mail className="h-6 w-6" />,
                info: '<EMAIL>',
              },
              {
                title: 'Call Us',
                icon: <Phone className="h-6 w-6" />,
                info: '00971508442283',
              },
              {
                title: 'Visit Us',
                icon: <MapPin className="h-6 w-6" />,
                info: 'V09 Russian Cluster international City, Dubai, UAE',
              },
            ].map((contact, index) => (
              <motion.div
                key={index}
                className="flex flex-col items-center text-center"
                variants={childVariants}
              >
                <div className="bg-secondary rounded-full p-3 mb-4 text-white">
                  {contact.icon}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  {contact.title}
                </h3>
                <p className="text-gray-600">{contact.info}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default About;
import React, { useState } from 'react';
import { MessageCircle, Mail, X, Phone } from 'lucide-react';

const FloatingContactWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const contactInfo = {
    whatsapp: '+971508442283', 
    email: '<EMAIL>',
    phone: '00971508442283' 
  };

  const handleWhatsAppClick = () => {
    const message = 'Hello! I am interested in your services.';
    const whatsappUrl = `https://wa.me/${contactInfo.whatsapp}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmailClick = () => {
    const subject = 'Inquiry about your services';
    const body = 'Hello,\n\nI would like more information about:\n\n';
    const mailtoUrl = `mailto:${contactInfo.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
  };

  const handlePhoneClick = () => {
    window.location.href = `tel:${contactInfo.phone}`;
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* Expanded contact options */}
        {isOpen && (
          <div className="absolute bottom-16 right-0 flex flex-col gap-3 mb-2">
            {/* WhatsApp Button */}
            <button
              onClick={handleWhatsAppClick}
              className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 flex items-center gap-3 group"
              title="Contact us on WhatsApp"
            >
              <MessageCircle className="w-6 h-6" />
              <span className="hidden group-hover:block whitespace-nowrap bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm absolute right-full mr-2">
                WhatsApp Chat
              </span>
            </button>

            {/* Email Button */}
            <button
              onClick={handleEmailClick}
              className="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 flex items-center gap-3 group"
              title="Send us an email"
            >
              <Mail className="w-6 h-6" />
              <span className="hidden group-hover:block whitespace-nowrap bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm absolute right-full mr-2">
                Send Email
              </span>
            </button>

            {/* Phone Button */}
            <button
              onClick={handlePhoneClick}
              className="bg-orange-500 hover:bg-orange-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 flex items-center gap-3 group"
              title="Call us now"
            >
              <Phone className="w-6 h-6" />
              <span className="hidden group-hover:block whitespace-nowrap bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm absolute right-full mr-2">
                Call Now
              </span>
            </button>
          </div>
        )}

        {/* Main toggle button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`bg-secondary hover:bg-secondary/90 text-white p-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 ${
            isOpen ? 'rotate-45' : ''
          }`}
          title="Contact options"
          aria-label="Contact options"
        >
          {isOpen ? <X className="w-6 h-6" /> : <MessageCircle className="w-6 h-6" />}
        </button>
      </div>
    </div>
  );
};

export default FloatingContactWidget;
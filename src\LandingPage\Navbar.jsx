import React, { useState, useEffect, useRef } from 'react';
import {
  Search, Mail, Phone,
  Facebook, Twitter, Rss, Instagram,
  ChevronDown, Menu, X
} from 'lucide-react';
import { useNavigate } from 'react-router-dom'; 
import Logo from '../assets/images/Logo.png';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isServicesDropdownOpen, setIsServicesDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const searchRef = useRef(null);
  const navigate = useNavigate();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    {
      name: 'Services',
      path: '/services',
      hasDropdown: true,
      dropdownItems: [
        { name: 'Floor & Wall Tiling Works', slug: 'floor-wall-tiling' },
        { name: 'Air-Conditioning, Ventilations & Air Filtration Systems Installation & Maintenance', slug: 'ac-ventilation' },
        { name: 'Engraving & Ornamentation Works', slug: 'engraving-ornamentation' },
        { name: 'Carpentry & Wood Flooring Works', slug: 'carpentry-wood' },
        { name: 'Insulation Contracting', slug: 'insulation' },
        { name: 'Electromechanical Equipment Installation and Maintenance', slug: 'electromechanical' },
        { name: 'Wallpaper Fixing Works', slug: 'wallpaper-fixing' },
        { name: 'Plaster Works', slug: 'plaster' },
        { name: 'Glass & Aluminum Installation & Maintenance', slug: 'glass-aluminum' },
        { name: 'Building Cleaning Services', slug: 'cleaning' },
        { name: 'False Ceiling & Light Partitions Installation', slug: 'false-ceiling' },
        { name: 'Plumbing & Sanitary Installation', slug: 'plumbing' },
      ]
    },
    { name: 'Contact', path: '/contact' }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle clicks outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.dropdown-container') && !event.target.closest('.services-button')) {
        setIsServicesDropdownOpen(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const toggleServicesDropdown = (e) => {
    e.stopPropagation();
    setIsServicesDropdownOpen((prev) => !prev);
  };

  const handleServiceClick = (slug) => {
    navigate(`/products/${slug}`);
    setIsServicesDropdownOpen(false);
    setIsMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMenuOpen((prev) => !prev);
    if (isMenuOpen) {
      setIsServicesDropdownOpen(false);
    }
  };

  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    if (query.length > 0) {
      const results = navItems
        .find(item => item.name === 'Services')?.dropdownItems
        .filter(service => 
          service.name.toLowerCase().includes(query.toLowerCase())
        ) || [];
      setSearchResults(results);
      setShowSearchResults(true);
    } else {
      setShowSearchResults(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchResults.length > 0) {
      handleServiceClick(searchResults[0].slug);
      setSearchQuery('');
      setShowSearchResults(false);
    }
  };
    const handleRequestQuote = () => {
    navigate('/contact'); 
  };

  const BrandHuntLogo = ({ isCompact = false }) => (
    <div className="flex items-center">
      <div className="h-10 overflow-hidden flex items-center">
        <img
          src={Logo}
          alt="Brand Hunt Logo"
          className="h-24 w-auto object-contain"
        />
      </div>
      <div className='-ml-2'>
        <h1 className={`font-bold text-gray-800 ${isCompact ? 'text-lg' : 'text-2xl'}`}>Brand Hunt</h1>
        {!isCompact && <p className="text-xs font-bold text-secondary uppercase tracking-wide">TECHNICAL SERVICES L.L.C</p>}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      <style>
        {`
          .dropdown-enter {
            opacity: 0;
            transform: translateY(-10px);
          }
          .dropdown-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 300ms ease-out, transform 300ms ease-out;
          }
          .dropdown-exit {
            opacity: 1;
            transform: translateY(0);
          }
          .dropdown-exit-active {
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 200ms ease-in, transform 200ms ease-in;
          }
          .dropdown-item:hover {
            background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
            transform: translateX(4px);
            transition: all 200ms ease;
          }
          .search-results {
            max-height: 300px;
            overflow-y: auto;
          }
        `}
      </style>

      {/* Top Bar */}
      <div className={`bg-custom-gradient text-textcolor shadow-sm transition-all duration-300 ${isScrolled ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100 p-2 px-5'}`}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Mail size={16} />
                <a href="mailto:<EMAIL>" className="text-sm text-white hover:underline">
    <EMAIL>
  </a>
            </div>
            <div className="flex items-center space-x-2">
              <Phone size={16} />
              <a href="tel:+00971508442283" className="text-sm text-white hover:underline">
    00971508442283</a>
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <div className="flex space-x-3">
              <Facebook size={20} className="hover:text-secondary cursor-pointer transition-colors" />
              <Twitter size={20} className="hover:text-secondary cursor-pointer transition-colors" />
              <Rss size={20} className="hover:text-secondary cursor-pointer transition-colors" />
              <Instagram size={20} className="hover:text-secondary cursor-pointer transition-colors" />
            </div>
            <button 
            onClick={handleRequestQuote}
            className="bg-secondary text-textcolor px-4 py-2 text-sm font-medium hover:bg-secondary/80 transition-colors rounded">
              Get a Quote
            </button>
          </div>
        </div>
      </div>

      {/* Sticky Header */}
      <div className={`transition-all duration-300 ${isScrolled ? 'fixed top-0 left-0 right-0 z-50 py-3 shadow-md bg-textcolor' : 'relative py-4 bg-textcolor'}`}>
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between">
            <BrandHuntLogo isCompact={isScrolled} />

            <nav className="hidden lg:flex items-center space-x-8 transition-all duration-300">
              {navItems.map((item, index) => (
                <div key={index} className="relative">
                  {item.hasDropdown ? (
                    <>
                      <button
                        className={`flex items-center space-x-1 text-primary hover:text-secondary font-medium transition-all duration-200 border-b-2 border-transparent hover:border-secondary ${item.name === 'Services' ? 'services-button' : ''}`}
                        onClick={toggleServicesDropdown}
                      >
                        <span>{item.name}</span>
                        {item.hasDropdown && <ChevronDown size={16} />}
                      </button>
                      {isServicesDropdownOpen && (
                        <div
                          className="dropdown-container absolute top-full left-0 mt-2 w-96 z-50 bg-white shadow-xl border border-gray-100 rounded-lg overflow-hidden"
                        >
                          <div className="flex flex-col max-h-96 overflow-y-auto p-2">
                            {item.dropdownItems.map((service, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleServiceClick(service.slug)}
                                className="dropdown-item w-full text-left block px-4 py-2 text-gray-700 hover:text-secondary text-sm font-medium rounded-md transition-colors"
                              >
                                {service.name}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <button
                      onClick={() => navigate(item.path)}
                      className="flex items-center space-x-1 text-primary hover:text-secondary font-medium transition-all duration-200 border-b-2 border-transparent hover:border-secondary"
                    >
                      <span>{item.name}</span>
                    </button>
                  )}
                </div>
              ))}
            </nav>

            {/* Search & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="hidden lg:block relative" ref={searchRef}>
                <form onSubmit={handleSearchSubmit} className="relative">
                  <input
                    type="text"
                    placeholder="Search services..."
                    className="w-52 px-4 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    onFocus={() => searchQuery.length > 0 && setShowSearchResults(true)}
                  />
                  <button type="submit" className="absolute right-3 top-2 text-gray-500 hover:text-secondary">
                    <Search size={18} />
                  </button>
                </form>
                {showSearchResults && searchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white shadow-lg rounded-md z-50 search-results w-52">
                    {searchResults.map((result, index) => (
                      <button
                        key={index}
                        className="w-full text-left px-4 py-2 hover:bg-gray-100 text-base"
                        onClick={() => {
                          handleServiceClick(result.slug);
                          setSearchQuery('');
                          setShowSearchResults(false);
                        }}
                      >
                        {result.name}
                      </button>
                    ))}
                  </div>
                )}
                {showSearchResults && searchResults.length === 0 && searchQuery.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white shadow-lg rounded-md z-50 p-4 text-sm text-gray-500">
                    No services found
                  </div>
                )}
              </div>

              <button
                className="lg:hidden p-2 text-gray-600 hover:text-secondary transition-colors"
                onClick={toggleMobileMenu}
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Nav */}
          {isMenuOpen && (
            <div className="lg:hidden mt-4 border-t border-gray-200 pt-4">
              <nav className="space-y-4">
                {navItems.map((item, index) => (
                  <div key={index}>
                    {item.hasDropdown ? (
                      <>
                        <button
                          className="flex items-center justify-between w-full text-left text-primary hover:text-secondary font-medium transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleServicesDropdown(e);
                          }}
                        >
                          <span>{item.name}</span>
                          {item.hasDropdown && <ChevronDown size={16} />}
                        </button>
                        {isServicesDropdownOpen && (
                          <div className="ml-4 mt-2 flex flex-col space-y-2">
                            {item.dropdownItems.map((service, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleServiceClick(service.slug)}
                                className="block pl-2 py-1 text-sm text-gray-700 hover:text-secondary text-left"
                              >
                                {service.name}
                              </button>
                            ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <button
                        onClick={() => {
                          navigate(item.path);
                          setIsMenuOpen(false);
                        }}
                        className="flex items-center justify-between w-full text-left text-primary hover:text-secondary font-medium transition-colors"
                      >
                        <span>{item.name}</span>
                      </button>
                    )}
                  </div>
                ))}
                <div className="pt-4 border-t border-gray-200">
                  <button className="bg-secondary text-textcolor px-6 py-2 text-sm font-medium hover:bg-secondary/70 transition-colors rounded w-full">
                    Get a Quote
                  </button>
                </div>
              </nav>
            </div>
          )}
        </div>
      </div>

      {isScrolled && <div className="h-16 lg:h-20"></div>}
    </div>
  );
};

export default Navbar;
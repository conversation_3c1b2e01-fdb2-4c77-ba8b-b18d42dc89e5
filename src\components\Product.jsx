// import React from 'react';
// import { ArrowRight } from 'lucide-react';
// import { Link } from 'react-router-dom';
// import productList from '../data/ProductList';

// const Product = () => {
//   return (
//     <section className="py-12 sm:py-16 px-4 sm:px-6 md:px-20 bg-gray-100">
//       <style>
//         {`
//           .card-hover {
//             transition: transform 0.3s ease, box-shadow 0.3s ease;
//           }
//           .card-hover:hover {
//             transform: translateY(-8px);
//             box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
//           }
//           .image-hover {
//             transition: transform 0.3s ease;
//           }
//           .card-hover:hover .image-hover {
//             transform: scale(1.05);
//           }
//           .product-name {
//             min-height: 2.5rem;
//             display: -webkit-box;
//             -webkit-line-clamp: 2;
//             -webkit-box-orient: vertical;
//             overflow: hidden;
//             text-overflow: ellipsis;
//           }
//           .gradient-overlay {
//             background: linear-gradient(90deg, rgba(249, 115, 22, 0.8), rgba(249, 115, 22, 0.8));
//             transition: background 0.3s ease;
//           }
//           .card-hover:hover .gradient-overlay {
//             background: linear-gradient(90deg, rgba(234, 88, 12, 0.9), rgba(194, 65, 12, 0.9));
//           }
//         `}
//       </style>

//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="text-center mb-10 sm:mb-12">
//           <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4 font-roboto">Our Services</h2>
//           <div className="w-16 sm:w-24 h-1 bg-secondary mx-auto"></div>
//         </div>

//         {/* Product Cards */}
//         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
//           {productList.map((product) => (
//             <Link
//               key={product.id}
//               to={`/products/${product.slug}`}
//               className="card-hover bg-white rounded-xl overflow-hidden shadow-md"
//             >
//               <div className="relative aspect-[4/3] overflow-hidden">
//                 <img
//                   src={product.heroImage}
//                   alt={product.title}
//                   className="image-hover w-full h-full object-cover"
//                 />
//               </div>
//               <div className="gradient-overlay p-4 flex items-center justify-between text-white h-16">
//                 <h3 className="text-sm sm:text-base font-bold uppercase product-name font-roboto">
//                   {product.title}
//                 </h3>
//               </div>
//             </Link>
//           ))}
//         </div>
//           <div className="text-center mt-10 sm:mt-16">
//             <button
//               onClick={() => setShowAll(true)}
//               className="bg-secondary text-white px-6 sm:px-8 py-2 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 hover:bg-secondary/45 font-roboto"
//             >
//               View All Products
//             </button>
//           </div>
//       </div>
//     </section>
//   );
// };

// export default Product;
import React, { useState, useEffect } from 'react';
import { ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import productList from '../data/ProductList';
import { motion, AnimatePresence } from 'framer-motion';

const Product = () => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [autoPlay, setAutoPlay] = useState(true);

  // Auto-advance the carousel
  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prev) => (prev + 1) % productList.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [autoPlay, productList.length]);

  // Check for reduced motion preference
  const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  // Animation variants for the grid container
  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2, // Stagger grid items
        delayChildren: 0.3, // Delay before children start animating
        when: 'beforeChildren',
      },
    },
  };

  // Animation variants for individual grid items
  const item = {
    hidden: { opacity: 0, y: 30, rotateX: isReducedMotion ? 0 : -15 },
    visible: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  // Animation variants for carousel items
  const carouselItem = {
    hidden: (direction) => ({
      opacity: 0,
      x: direction > 0 ? 150 : -150,
      rotateY: isReducedMotion ? 0 : direction > 0 ? 30 : -30,
      scale: 0.85,
    }),
    visible: {
      opacity: 1,
      x: 0,
      rotateY: 0,
      scale: 1,
      transition: {
        duration: 0.7,
        ease: 'easeOut',
        type: 'spring',
        stiffness: 100,
        staggerChildren: 0.2, // Stagger children (image and text)
      },
    },
    exit: (direction) => ({
      opacity: 0,
      x: direction > 0 ? -150 : 150,
      rotateY: isReducedMotion ? 0 : direction > 0 ? -30 : 30,
      scale: 0.85,
      transition: {
        duration: 0.5,
        ease: 'easeIn',
      },
    }),
  };

  // Animation variants for carousel child elements (image and text)
  const carouselChild = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  };

  // Animation variants for indicators
  const indicatorVariants = {
    active: {
      scale: 1.3,
      backgroundColor: '#f97316',
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    inactive: {
      scale: 1,
      backgroundColor: '#d1d5db',
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    hover: {
      scale: 1.5,
      backgroundColor: '#fb923c',
      transition: { duration: 0.2 },
    },
  };

  // Animation variants for header and button
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.2,
      },
    },
  };

  const sectionChild = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  const handleViewAll = () => {
    navigate('/services');
  };

  const nextProduct = () => {
    setDirection(1);
    setCurrentIndex((prev) => (prev + 1) % productList.length);
  };

  const prevProduct = () => {
    setDirection(-1);
    setCurrentIndex((prev) => (prev - 1 + productList.length) % productList.length);
  };

  return (
    <section className="py-12 sm:py-16 px-4 sm:px-6 md:px-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
      <style>
        {`
          .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          .card-hover:hover {
            transform: translateY(-10px) rotateX(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
          }
          .image-hover {
            transition: transform 0.5s ease;
          }
          .card-hover:hover .image-hover {
            transform: scale(1.1) rotate(2deg);
          }
          .product-name {
            min-height: 2.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .gradient-overlay {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.9), rgba(234, 88, 12, 0.9));
            transition: all 0.3s ease;
          }
          .card-hover:hover .gradient-overlay {
            background: linear-gradient(135deg, rgba(234, 88, 12, 1), rgba(194, 65, 12, 1));
          }
          .carousel-container {
            perspective: 1200px;
          }
          .glow-effect {
            box-shadow: 0 0 20px rgba(249, 115, 22, 0.5);
          }
          .bg-animation {
            background: radial-gradient(circle at 50% 50%, rgba(249, 115, 22, 0.1), transparent);
            animation: pulse 10s infinite ease-in-out;
          }
          @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.2); opacity: 0.5; }
          }
        `}
      </style>

      {/* Subtle background animation */}
      <motion.div
        className="absolute inset-0 bg-animation"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ duration: 2 }}
      />

      <div className="max-w-7xl mx-auto relative">
        {/* Header */}
        <motion.div
          className="text-center mb-10 sm:mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionVariants}
        >
          <motion.h2
            className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4 font-roboto"
            variants={sectionChild}
          >
            Our Premium Services
          </motion.h2>
          <motion.p
            className="text-gray-600 max-w-2xl mx-auto mb-6"
            variants={sectionChild}
          >
            Discover our range of exceptional services designed to meet your needs
          </motion.p>
          <motion.div
            className="w-16 sm:w-24 h-1 bg-gradient-to-r from-orange-500 to-orange-600 mx-auto rounded-full"
            variants={sectionChild}
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          />
        </motion.div>

        {/* Carousel for single product display */}
        <div
          className="mb-16 relative carousel-container"
          onMouseEnter={() => setAutoPlay(false)}
          onMouseLeave={() => setAutoPlay(true)}
        >
          <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl glow-effect">
            <AnimatePresence custom={direction} initial={false} mode="wait">
              <motion.div
                key={currentIndex}
                custom={direction}
                variants={carouselItem}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="absolute inset-0"
              >
                <div className="h-full flex flex-col md:flex-row bg-white rounded-2xl overflow-hidden">
                  <motion.div
                    className="md:w-1/2 h-64 md:h-full relative overflow-hidden"
                    variants={carouselChild}
                  >
                    <motion.img
                      src={productList[currentIndex].heroImage}
                      alt={productList[currentIndex].title}
                      className="image-hover w-full h-full object-cover"
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      transition={{ duration: 0.4 }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                  </motion.div>
                  <motion.div
                    className="md:w-1/2 p-6 md:p-8 flex flex-col justify-center bg-gradient-to-br from-gray-50 to-white"
                    variants={carouselChild}
                  >
                    <motion.h3
                      className="text-2xl md:text-3xl font-bold text-gray-800 mb-4 font-roboto"
                      variants={carouselChild}
                    >
                      {productList[currentIndex].title}
                    </motion.h3>
                    <motion.p className="text-gray-600 mb-6" variants={carouselChild}>
                      {productList[currentIndex].description ||
                        'Premium service with exceptional quality and results.'}
                    </motion.p>
                    <Link
                      to={`/products/${productList[currentIndex].slug}`}
                      className="inline-flex items-center text-orange-600 font-semibold group"
                    >
                      Learn more
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-2" />
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation arrows */}
          <motion.button
            onClick={prevProduct}
            className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-all duration-300"
            whileHover={{ scale: 1.2, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="h-6 w-6 text-gray-800" />
          </motion.button>
          <motion.button
            onClick={nextProduct}
            className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-all duration-300"
            whileHover={{ scale: 1.2, rotate: -5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="h-6 w-6 text-gray-800" />
          </motion.button>

          {/* Indicators */}
          <motion.div
            className="flex justify-center mt-6 space-x-2"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={sectionVariants}
          >
            {productList.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => {
                  setDirection(index > currentIndex ? 1 : -1);
                  setCurrentIndex(index);
                }}
                variants={indicatorVariants}
                initial="inactive"
                animate={index === currentIndex ? 'active' : 'inactive'}
                whileHover="hover"
                className="w-3 h-3 rounded-full"
              />
            ))}
          </motion.div>
        </div>

        {/* Grid of all products */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }} 
          variants={container}
        >
          {productList.map((product) => (
            <motion.div
              key={product.id}
              variants={item}
              whileHover={{
                y: -8,
                rotateX: isReducedMotion ? 0 : 10,
                rotateY: isReducedMotion ? 0 : 35,
                transition: { duration: 0.3 },
              }}
            >
              <Link
                to={`/products/${product.slug}`}
                className="card-hover bg-white rounded-2xl overflow-hidden shadow-lg flex flex-col h-full"
              >
                <div className="relative aspect-[4/3] overflow-hidden">
                  <motion.img
                    src={product.heroImage}
                    alt={product.title}
                    className="image-hover w-full h-full object-cover"
                    whileHover={{ scale: 1.1, rotate: 3 }}
                    transition={{ duration: 0.4 }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
                <div className="gradient-overlay p-4 flex items-center justify-between text-white flex-grow">
                  <h3 className="text-sm sm:text-base font-bold uppercase product-name font-roboto">
                    {product.title}
                  </h3>
                  <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-2" />
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div
          className="text-center mt-12 sm:mt-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionVariants}
        >
          <motion.button
            onClick={handleViewAll}
            className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 sm:px-10 py-3 sm:py-4 rounded-xl font-semibold text-base transition-all duration-300 hover:shadow-lg hover:from-orange-600 hover:to-orange-700 font-roboto flex items-center mx-auto group"
            variants={sectionChild}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Services
            <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-2" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Product;
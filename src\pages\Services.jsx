import React, { useState, useEffect } from 'react';
import { ArrowR<PERSON>, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import productList from '../data/ProductList';
import { motion, AnimatePresence } from 'framer-motion';

const Services = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [autoPlay, setAutoPlay] = useState(true);


  useEffect(() => {
    if (!autoPlay) return;
    
    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex(prev => (prev + 1) % productList.length);
    }, 4000);
    
    return () => clearInterval(interval);
  }, [autoPlay, productList.length]);

  const isReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;

  const headerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        when: "beforeChildren"
      }
    }
  };

  const headerItem = {
    hidden: { opacity: 0, y: -30, scale: isReducedMotion ? 1 : 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        type: isReducedMotion ? "tween" : "spring",
        stiffness: 100
      }
    }
  };

  const underlineVariants = {
    hidden: { width: 0, opacity: 0 },
    visible: {
      width: "100%",
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    },
    pulse: {
      boxShadow: isReducedMotion ? "none" : "0 0 10px rgba(249, 115, 22, 0.5)",
      transition: { duration: 1, repeat: Infinity, repeatType: "reverse" }
    }
  };

  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        when: "beforeChildren"
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 30, rotateX: isReducedMotion ? 0 : -15 },
    visible: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: { 
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const carouselItem = {
    hidden: (direction) => ({
      opacity: 0,
      x: direction > 0 ? 150 : -150,
      rotateY: direction > 0 ? 30 : -30,
      scale: 0.85
    }),
    visible: {
      opacity: 1,
      x: 0,
      rotateY: 0,
      scale: 1,
      transition: {
        duration: 0.7,
        ease: "easeOut",
        type: "spring",
        stiffness: 100
      }
    },
    exit: (direction) => ({
      opacity: 0,
      x: direction > 0 ? -150 : 150,
      rotateY: direction > 0 ? -30 : 30,
      scale: 0.85,
      transition: {
        duration: 0.5,
        ease: "easeIn"
      }
    })
  };

  const indicatorVariants = {
    active: {
      scale: 1.3,
      backgroundColor: "#f97316",
      transition: { duration: 0.3, ease: "easeOut" }
    },
    inactive: {
      scale: 1,
      backgroundColor: "#d1d5db",
      transition: { duration: 0.3, ease: "easeOut" }
    },
    hover: {
      scale: 1.5,
      backgroundColor: "#fb923c",
      transition: { duration: 0.2 }
    }
  };

  const nextService = () => {
    setDirection(1);
    setCurrentIndex(prev => (prev + 1) % productList.length);
  };

  const prevService = () => {
    setDirection(-1);
    setCurrentIndex(prev => (prev - 1 + productList.length) % productList.length);
  };

  return (
    <section className="py-12 sm:py-16 px-4 sm:px-6 md:px-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
      <style>
        {`
          .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          .card-hover:hover {
            transform: translateY(-10px) rotateX(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
          }
          .image-hover {
            transition: transform 0.5s ease;
          }
          .card-hover:hover .image-hover {
            transform: scale(1.1) rotate(2deg);
          }
          .service-name {
            min-height: 2.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .gradient-overlay {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.9), rgba(234, 88, 12, 0.9));
            transition: all 0.3s ease;
          }
          .card-hover:hover .gradient-overlay {
            background: linear-gradient(135deg, rgba(234, 88, 12, 1), rgba(194, 65, 12, 1));
          }
          .carousel-container {
            perspective: 1200px;
          }
          .glow-effect {
            box-shadow: 0 0 20px rgba(249, 115, 22, 0.5);
          }
          .bg-animation {
            background: radial-gradient(circle at 50% 50%, rgba(249, 115, 22, 0.1), transparent);
            animation: pulse 10s infinite ease-in-out;
          }
          @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.2); opacity: 0.5; }
          }
        `}
      </style>

      {/* Subtle background animation */}
      <motion.div
        className="absolute inset-0 bg-animation"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ duration: 2 }}
      />

      <div className="max-w-7xl mx-auto relative">
        {/* Header */}
        <motion.div 
          className="text-center mb-10 sm:mb-12"
          variants={headerContainer}
          initial="hidden"
          animate="visible"
        >
          <motion.h2 
            className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4 font-roboto"
            variants={headerItem}
          >
            Explore Our Services
          </motion.h2>
          <motion.p 
            className="text-gray-600 max-w-2xl mx-auto mb-6"
            variants={headerItem}
          >
            Discover our comprehensive range of services tailored to your needs
          </motion.p>
          <motion.div 
            className="w-16 sm:w-24 h-1 bg-gradient-to-r from-orange-500 to-orange-600 mx-auto rounded-full"
            variants={underlineVariants}
            initial="hidden"
            animate={["visible", "pulse"]}
          />
        </motion.div>

        {/* Carousel for single service display */}
        <div className="mb-16 relative carousel-container" 
             onMouseEnter={() => setAutoPlay(false)} 
             onMouseLeave={() => setAutoPlay(true)}>
          <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl glow-effect">
            <AnimatePresence custom={direction} initial={false} mode="wait">
              <motion.div
                key={currentIndex}
                custom={direction}
                variants={carouselItem}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="absolute inset-0"
              >
                <div className="h-full flex flex-col md:flex-row bg-white rounded-2xl overflow-hidden">
                  <div className="md:w-1/2 h-64 md:h-full relative overflow-hidden">
                    <motion.img
                      src={productList[currentIndex].heroImage}
                      alt={productList[currentIndex].title}
                      className="image-hover w-full h-full object-cover"
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      transition={{ duration: 0.4 }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                  </div>
                  <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-center bg-gradient-to-br from-gray-50 to-white">
                    <motion.h3 
                      className="text-2xl md:text-3xl font-bold text-gray-800 mb-4 font-roboto"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {productList[currentIndex].title}
                    </motion.h3>
                    <motion.p 
                      className="text-gray-600 mb-6"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      {productList[currentIndex].description || "Premium service with exceptional quality and results."}
                    </motion.p>
                    <Link
                      to={`/products/${productList[currentIndex].slug}`}
                      className="inline-flex items-center text-orange-600 font-semibold group"
                    >
                      Learn more
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-2" />
                    </Link>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
          
          {/* Navigation arrows */}
          <motion.button 
            onClick={prevService}
            className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-all duration-300"
            whileHover={{ scale: 1.2, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="h-6 w-6 text-gray-800" />
          </motion.button>
          <motion.button 
            onClick={nextService}
            className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-all duration-300"
            whileHover={{ scale: 1.2, rotate: -5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="h-6 w-6 text-gray-800" />
          </motion.button>
          
          {/* Indicators */}
          <div className="flex justify-center mt-6 space-x-2">
            {productList.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => {
                  setDirection(index > currentIndex ? 1 : -1);
                  setCurrentIndex(index);
                }}
                variants={indicatorVariants}
                initial="inactive"
                animate={index === currentIndex ? "active" : "inactive"}
                whileHover="hover"
                className="w-3 h-3 rounded-full"
              />
            ))}
          </div>
        </div>

        {/* Grid of all services */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8"
          variants={container}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-50px" }}
        >
          {productList.map((service, index) => (
            <motion.div
              key={service.id}
              variants={item}
              whileHover={{ 
                y: -8, 
                rotateX: isReducedMotion ? 0 : 10, 
                rotateY: isReducedMotion ? 0 : 5,
                transition: { duration: 0.3 }
              }}
            >
              <Link
                to={`/products/${service.slug}`}
                className="card-hover bg-white rounded-2xl overflow-hidden shadow-lg flex flex-col h-full"
              >
                <div className="relative aspect-[4/3] overflow-hidden">
                  <motion.img
                    src={service.heroImage}
                    alt={service.title}
                    className="image-hover w-full h-full object-cover"
                    whileHover={{ scale: 1.1, rotate: 3 }}
                    transition={{ duration: 0.4 }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
                <div className="gradient-overlay p-4 flex items-center justify-between text-white flex-grow">
                  <h3 className="text-sm sm:text-base font-bold uppercase service-name font-roboto">
                    {service.title}
                  </h3>
                  <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-2" />
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import Navbar from './LandingPage/Navbar';
import ProductPage from './pages/ProductPages';
import About from './pages/About';
import Contact from './pages/ContactUs';
import Footer from './LandingPage/Footer';
import FloatingContactWidget from './components/FloatingContactWidget';
import Services from './pages/Services';

const App = () => {
  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/services" element={<Services />} />
            <Route path="/products/:slug" element={<ProductPage />} />
            <Route path="/contact" element={<Contact />} />
          </Routes>
        </main>
        
        <Footer />
        <FloatingContactWidget />
      </div>
    </Router>
  );
};

export default App;